@file:Suppress("Unused")

package com.fibelatti.ui.foundation

import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp

@Composable
fun Int.dpToPx(): Float = with(LocalDensity.current) { this@dpToPx * density }

@Composable
fun Dp.dpToPx(): Float = with(LocalDensity.current) { <EMAIL>() }

@Composable
fun Int.pxToDp(): Dp = with(LocalDensity.current) { <EMAIL>() }
