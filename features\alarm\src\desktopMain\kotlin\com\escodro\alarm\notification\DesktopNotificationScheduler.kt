package com.escodro.alarm.notification

import com.escodro.alarm.model.Task

internal class DesktopNotificationScheduler : NotificationScheduler {

    override fun scheduleTaskNotification(task: Task, timeInMillis: Long) {
        // TODO: Implement scheduleTaskNotification
    }

    override fun cancelTaskNotification(task: Task) {
        // TODO: Implement cancelTaskNotification
    }

    override fun updateTaskNotification(task: Task) {
        // TODO: Implement updateTaskNotification
    }
}
