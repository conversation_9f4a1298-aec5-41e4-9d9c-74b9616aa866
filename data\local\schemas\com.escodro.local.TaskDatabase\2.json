{"formatVersion": 1, "database": {"version": 2, "identityHash": "d704a493422860582049436ff4dd4de9", "entities": [{"tableName": "Task", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`task_id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `task_is_completed` INTEGER NOT NULL, `task_title` TEXT NOT NULL, `task_description` TEXT, `task_category_id` INTEGER, `task_due_date` INTEGER, `task_creation_date` INTEGER, `task_completed_date` INTEGER, FOREIGN KEY(`task_category_id`) REFERENCES `Category`(`category_id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "id", "columnName": "task_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "completed", "columnName": "task_is_completed", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "title", "columnName": "task_title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "description", "columnName": "task_description", "affinity": "TEXT", "notNull": false}, {"fieldPath": "categoryId", "columnName": "task_category_id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "dueDate", "columnName": "task_due_date", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "creationDate", "columnName": "task_creation_date", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "completedDate", "columnName": "task_completed_date", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"columnNames": ["task_id"], "autoGenerate": true}, "indices": [{"name": "index_Task_task_category_id", "unique": false, "columnNames": ["task_category_id"], "createSql": "CREATE  INDEX `index_Task_task_category_id` ON `${TABLE_NAME}` (`task_category_id`)"}], "foreignKeys": [{"table": "Category", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["task_category_id"], "referencedColumns": ["category_id"]}]}, {"tableName": "Category", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`category_id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `category_name` TEXT, `category_color` TEXT)", "fields": [{"fieldPath": "id", "columnName": "category_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "category_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "color", "columnName": "category_color", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["category_id"], "autoGenerate": true}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'd704a493422860582049436ff4dd4de9')"]}}