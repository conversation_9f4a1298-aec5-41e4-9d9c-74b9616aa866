package com.example.weatherwidgetapp.widget

import android.app.Activity
import android.appwidget.AppWidgetManager
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.glance.appwidget.GlanceAppWidgetManager
import com.example.weatherwidgetapp.presentation.widget.config.WidgetConfigScreen
import com.example.weatherwidgetapp.presentation.widget.config.WidgetConfigViewModel
import com.example.weatherwidgetapp.ui.theme.WeatherWidgetAppTheme
import org.koin.androidx.viewmodel.ext.android.viewModel

class WeatherWidgetConfigActivity : ComponentActivity() {

    private val viewModel: WidgetConfigViewModel by viewModel()
    private var appWidgetId = AppWidgetManager.INVALID_APPWIDGET_ID

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Get the widget ID from the intent.
        appWidgetId = intent?.extras?.getInt(
            AppWidgetManager.EXTRA_APPWIDGET_ID,
            AppWidgetManager.INVALID_APPWIDGET_ID
        ) ?: AppWidgetManager.INVALID_APPWIDGET_ID

        // If the widget ID is invalid, close the activity.
        if (appWidgetId == AppWidgetManager.INVALID_APPWIDGET_ID) {
            finish()
            return
        }

        setContent {
            WeatherWidgetAppTheme {
                WidgetConfigScreen(
                    viewModel = viewModel,
                    onSaveConfiguration = { saveConfigurationAndFinish() },
                    onCancel = { cancelConfiguration() }
                )
            }
        }

        // Initialize configuration for this widget
        viewModel.initializeConfiguration(appWidgetId)
    }

    private fun saveConfigurationAndFinish() {
        val context = this

        kotlinx.coroutines.MainScope().launch {
            try {
                // Update the widget with new configuration
                val glanceId = GlanceAppWidgetManager(context).getGlanceIdBy(appWidgetId)
                WeatherWidget().update(context, glanceId)

                // Schedule the worker if needed
                WeatherUpdateWorker.enqueue(context)

                // Return success result
                val resultValue = Intent().putExtra(AppWidgetManager.EXTRA_APPWIDGET_ID, appWidgetId)
                setResult(Activity.RESULT_OK, resultValue)
                finish()
            } catch (e: Exception) {
                // Handle error - could show a snackbar or toast
                setResult(Activity.RESULT_CANCELED)
                finish()
            }
        }
    }

    private fun cancelConfiguration() {
        setResult(Activity.RESULT_CANCELED)
        finish()
    }
}

