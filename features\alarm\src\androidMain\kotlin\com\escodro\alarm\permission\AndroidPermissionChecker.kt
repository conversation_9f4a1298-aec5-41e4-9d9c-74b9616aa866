package com.escodro.alarm.permission

import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import com.escodro.alarm.extension.getAlarmManager

internal class AndroidPermissionChecker(private val context: Context) : Permission<PERSON><PERSON><PERSON> {

    override fun checkPermission(permission: String): Boolean =
        ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED

    @RequiresApi(Build.VERSION_CODES.S)
    override fun canScheduleExactAlarms(): <PERSON><PERSON>an {
        val alarmManager = context.getAlarmManager() ?: return false
        return alarmManager.canScheduleExactAlarms()
    }
}
