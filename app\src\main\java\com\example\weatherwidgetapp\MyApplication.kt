package com.example.weatherwidgetapp

import android.app.Application
import com.example.weatherwidgetapp.di.appModule
import com.example.weatherwidgetapp.di.widgetModule
import com.example.weatherwidgetapp.di.widgetConfigModule
import org.koin.android.ext.koin.androidContext
import org.koin.android.ext.koin.androidLogger
import org.koin.androidx.workmanager.koin.workManagerFactory
import org.koin.core.context.startKoin
import org.koin.core.logger.Level

class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()

        startKoin {
            androidLogger(Level.DEBUG) // Use Level.INFO or Level.NONE in release
            androidContext(this@MyApplication)
            workManagerFactory() // Initialize Koin for WorkManager
            modules(appModule, widgetModule, widgetConfigModule)
        }
    }
}