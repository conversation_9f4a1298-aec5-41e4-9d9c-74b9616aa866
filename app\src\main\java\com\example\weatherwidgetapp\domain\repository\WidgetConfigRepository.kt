package com.example.weatherwidgetapp.domain.repository

import com.example.weatherwidgetapp.domain.model.WidgetConfiguration
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for widget configuration operations
 */
interface WidgetConfigRepository {
    
    /**
     * Get widget configuration for a specific app widget ID
     */
    fun getWidgetConfiguration(appWidgetId: Int): Flow<WidgetConfiguration?>
    
    /**
     * Save widget configuration
     */
    suspend fun saveWidgetConfiguration(configuration: WidgetConfiguration)
    
    /**
     * Delete widget configuration for a specific app widget ID
     */
    suspend fun deleteWidgetConfiguration(appWidgetId: Int)
    
    /**
     * Get all widget configurations
     */
    fun getAllWidgetConfigurations(): Flow<List<WidgetConfiguration>>
}
