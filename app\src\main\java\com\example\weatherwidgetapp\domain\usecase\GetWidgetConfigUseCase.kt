package com.example.weatherwidgetapp.domain.usecase

import com.example.weatherwidgetapp.domain.model.WidgetConfiguration
import com.example.weatherwidgetapp.domain.repository.WidgetConfigRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

/**
 * Use case for retrieving widget configuration
 */
class GetWidgetConfigUseCase(
    private val repository: WidgetConfigRepository
) {
    
    /**
     * Get widget configuration for a specific app widget ID
     * Returns default configuration if none exists
     */
    operator fun invoke(appWidgetId: Int): Flow<WidgetConfiguration> {
        return repository.getWidgetConfiguration(appWidgetId)
            .map { config ->
                config ?: WidgetConfiguration(appWidgetId = appWidgetId)
            }
    }
}
