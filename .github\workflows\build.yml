name: Build project

on:
  push:
    branches:
      - main
    paths-ignore:
      - '**.md'
  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build-android:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'adopt'

      - name: Build with Gradle
        run: ./gradlew clean build test -x shared:testDebugUnitTest -x shared:testReleaseUnitTest -x shared:desktopTest --parallel
        env:
          ALKAA_KEY_ALIAS: ${{ secrets.ALKAA_KEY_ALIAS }}
          ALKAA_KEY_PASSWORD: ${{ secrets.ALKAA_KEY_PASSWORD }}
          ALKAA_KEY_STORE_PASSWORD: ${{ secrets.ALKAA_KEY_STORE_PASSWORD }}
          ALKAA_STORE_PATH: ${{ vars.ALKAA_STORE_PATH }}

  build-ios:
    runs-on: macos-14

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'adopt'

      - name: Build with Xcode
        run: xcodebuild -workspace ios-app/alkaa.xcodeproj/project.xcworkspace -configuration Debug -scheme alkaa -sdk iphonesimulator

  build-desktop:
    runs-on: macos-14

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          java-version: '19'
          distribution: 'temurin'

      - name: Build with Gradle
        run: ./gradlew desktop-app:createDistributable
