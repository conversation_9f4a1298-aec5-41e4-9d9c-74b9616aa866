<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/widget_background"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="sans-serif-medium"
        android:paddingBottom="4dp"
        android:text="Title"
        android:textColor="@color/text"
        android:textSize="18sp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="2dp"
        android:text="The text of the note will be displayed here."
        android:textColor="@color/text_secondary"
        android:textSize="16sp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="27 september 2020"
        android:textAlignment="textEnd"
        android:textColor="@color/text_secondary"
        android:textSize="14sp" />


</LinearLayout>