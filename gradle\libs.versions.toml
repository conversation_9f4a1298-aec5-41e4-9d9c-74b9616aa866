[versions]
agp = "8.10.0"
coil = "3.2.0"
coroutines = "1.10.2"
hilt = "2.56.2"
hiltExtensions = "1.2.0"
kotlin = "2.1.21"
room = "2.7.1"
aboutLibraries = "12.1.2"

[libraries]
kotlin = { module = "org.jetbrains.kotlin:kotlin-stdlib-jdk8", version.ref = "kotlin" }
coroutines-core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "coroutines" }
coroutines-android = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-android", version.ref = "coroutines" }

appcompat = { module = "androidx.appcompat:appcompat", version = "1.7.0" }
activity = { module = "androidx.activity:activity", version = "1.10.1" }
core-ktx = { module = "androidx.core:core-ktx", version = "1.16.0" }
material = { module = "com.google.android.material:material", version = "1.12.0" }
lifecycle-runtime-compose = { module = "androidx.lifecycle:lifecycle-runtime-compose", version = "2.9.0" }
graphics-shapes = { module = "androidx.graphics:graphics-shapes", version = "1.0.1" }
palette = { module = "androidx.palette:palette-ktx", version = "1.0.0" }

compose-bom = { module = "androidx.compose:compose-bom", version = "2025.05.01" }
compose-runtime = { module = "androidx.compose.runtime:runtime" }
compose-material3 = { module = "androidx.compose.material3:material3" }
compose-ui = { module = "androidx.compose.ui:ui" }
compose-ui-tooling = { module = "androidx.compose.ui:ui-tooling" }
compose-ui-tooling-preview = { module = "androidx.compose.ui:ui-tooling-preview" }

core-library-desugaring = { module = "com.android.tools:desugar_jdk_libs", version = "2.1.5" }

dagger-hilt-android = { module = "com.google.dagger:hilt-android", version.ref = "hilt" }
dagger-hilt-compiler = { module = "com.google.dagger:hilt-compiler", version.ref = "hilt" }
hilt-navigation-compose = { module = "androidx.hilt:hilt-navigation-compose", version.ref = "hiltExtensions" }

room-runtime = { module = "androidx.room:room-ktx", version.ref = "room" }
room-compiler = { module = "androidx.room:room-compiler", version.ref = "room" }

ucrop = { module = "com.github.yalantis:ucrop", version = "2.2.10" }
coil = { module = "io.coil-kt.coil3:coil", version.ref = "coil" }
reorderable = { module = "sh.calvin.reorderable:reorderable", version = "2.4.3" }
colorpicker-compose = { module = "com.github.skydoves:colorpicker-compose", version = "1.1.2" }

about-libraries = { module = "com.mikepenz:aboutlibraries-compose", version.ref = "aboutLibraries" }

timber = { module = "com.jakewharton.timber:timber", version = "5.0.1" }
leakcanary = { module = "com.squareup.leakcanary:leakcanary-android", version = "2.14" }

compose-lint-checks = { module = "com.slack.lint.compose:compose-lint-checks", version = "1.4.2" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
android-library = { id = "com.android.library", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-parcelize = { id = "org.jetbrains.kotlin.plugin.parcelize", version.ref = "kotlin" }
compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
ksp = { id = "com.google.devtools.ksp", version = "2.1.21-2.0.1" }
hilt = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }
room = { id = "androidx.room", version.ref = "room" }
spotless = { id = "com.diffplug.spotless", version = "7.0.3" }
cache-fix = { id = "org.gradle.android.cache-fix", version = "3.0.1" }
about-libraries = { id = "com.mikepenz.aboutlibraries.plugin", version.ref = "aboutLibraries" }
licensee = { id = "app.cash.licensee", version = "1.13.0" }
