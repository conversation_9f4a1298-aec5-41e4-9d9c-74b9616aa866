package com.example.weatherwidgetapp.presentation.widget.config.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Cloud
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.weatherwidgetapp.domain.model.BackgroundStyle
import com.example.weatherwidgetapp.domain.model.WidgetConfiguration
import kotlinx.coroutines.flow.StateFlow
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Real-time preview component that shows how the widget will look
 */
@Composable
fun WidgetPreview(
    configurationFlow: StateFlow<WidgetConfiguration>,
    modifier: Modifier = Modifier
) {
    val configuration by configurationFlow.collectAsState()
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .height(120.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = if (configuration.hasRoundedCorners) {
            RoundedCornerShape(12.dp)
        } else {
            RoundedCornerShape(0.dp)
        }
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    color = getBackgroundColor(configuration.backgroundStyle),
                    shape = if (configuration.hasRoundedCorners) {
                        RoundedCornerShape(12.dp)
                    } else {
                        RoundedCornerShape(0.dp)
                    }
                )
                .alpha(configuration.backgroundTransparency)
                .padding(16.dp)
        ) {
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // City name (if enabled)
                if (configuration.showCityName) {
                    Text(
                        text = "Sample City",
                        fontSize = (16 * configuration.fontSize.scaleFactor).sp,
                        fontWeight = FontWeight.Bold,
                        color = getTextColor(configuration.backgroundStyle)
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                }
                
                // Weather info row
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Weather icon
                    Icon(
                        imageVector = Icons.Default.Cloud,
                        contentDescription = "Weather Icon",
                        modifier = Modifier.size((24 * configuration.iconSize.scaleFactor).dp),
                        tint = getTextColor(configuration.backgroundStyle)
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    // Temperature
                    Text(
                        text = "22°C",
                        fontSize = (20 * configuration.fontSize.scaleFactor).sp,
                        fontWeight = FontWeight.Medium,
                        color = getTextColor(configuration.backgroundStyle)
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    // Condition
                    Text(
                        text = "Cloudy",
                        fontSize = (14 * configuration.fontSize.scaleFactor).sp,
                        color = getTextColor(configuration.backgroundStyle)
                    )
                }
                
                // Time (if enabled)
                if (configuration.showTime) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = getCurrentTime(),
                        fontSize = (12 * configuration.fontSize.scaleFactor).sp,
                        color = getTextColor(configuration.backgroundStyle).copy(alpha = 0.8f)
                    )
                }
            }
        }
    }
}

@Composable
private fun getBackgroundColor(style: BackgroundStyle): Color {
    return when (style) {
        BackgroundStyle.DARK -> Color(0xFF1E1E1E)
        BackgroundStyle.LIGHT -> Color(0xFFF5F5F5)
        BackgroundStyle.BLUE -> Color(0xFF1976D2)
        BackgroundStyle.SYSTEM -> MaterialTheme.colorScheme.surface
    }
}

@Composable
private fun getTextColor(style: BackgroundStyle): Color {
    return when (style) {
        BackgroundStyle.DARK -> Color.White
        BackgroundStyle.LIGHT -> Color.Black
        BackgroundStyle.BLUE -> Color.White
        BackgroundStyle.SYSTEM -> MaterialTheme.colorScheme.onSurface
    }
}

private fun getCurrentTime(): String {
    val formatter = SimpleDateFormat("HH:mm", Locale.getDefault())
    return formatter.format(Date())
}
