package com.example.weatherwidgetapp.widget

import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.glance.GlanceModifier
import androidx.glance.background
import androidx.glance.cornerRadius
import androidx.glance.text.FontWeight
import androidx.glance.text.TextStyle
import androidx.glance.unit.ColorProvider
import com.example.weatherwidgetapp.domain.model.BackgroundStyle
import com.example.weatherwidgetapp.domain.model.FontSize
import com.example.weatherwidgetapp.domain.model.IconSize
import com.example.weatherwidgetapp.domain.model.WidgetConfiguration

/**
 * Helper class for applying widget configuration to Glance components
 */
object WidgetConfigurationHelper {

    /**
     * Apply background styling based on configuration
     */
    fun applyBackgroundStyle(
        modifier: GlanceModifier,
        configuration: WidgetConfiguration
    ): GlanceModifier {
        val backgroundColor = getBackgroundColor(configuration.backgroundStyle)
        val alpha = configuration.backgroundTransparency
        
        return modifier
            .background(ColorProvider(backgroundColor.copy(alpha = alpha)))
            .then(
                if (configuration.hasRoundedCorners) {
                    GlanceModifier.cornerRadius(12.dp)
                } else {
                    GlanceModifier
                }
            )
    }

    /**
     * Get text style based on configuration
     */
    fun getTextStyle(
        configuration: WidgetConfiguration,
        baseSize: Int = 16,
        fontWeight: FontWeight = FontWeight.Normal
    ): TextStyle {
        val scaledSize = (baseSize * configuration.fontSize.scaleFactor).sp
        val textColor = getTextColor(configuration.backgroundStyle)
        
        return TextStyle(
            fontSize = scaledSize,
            fontWeight = fontWeight,
            color = ColorProvider(textColor)
        )
    }

    /**
     * Get icon size based on configuration
     */
    fun getIconSize(configuration: WidgetConfiguration, baseSize: Int = 24): androidx.compose.ui.unit.Dp {
        return (baseSize * configuration.iconSize.scaleFactor).dp
    }

    /**
     * Get background color based on style
     */
    private fun getBackgroundColor(style: BackgroundStyle): Color {
        return when (style) {
            BackgroundStyle.DARK -> Color(0xFF1E1E1E)
            BackgroundStyle.LIGHT -> Color(0xFFF5F5F5)
            BackgroundStyle.BLUE -> Color(0xFF1976D2)
            BackgroundStyle.SYSTEM -> Color(0xFF2196F3) // Default system color
        }
    }

    /**
     * Get text color based on background style
     */
    private fun getTextColor(style: BackgroundStyle): Color {
        return when (style) {
            BackgroundStyle.DARK -> Color.White
            BackgroundStyle.LIGHT -> Color.Black
            BackgroundStyle.BLUE -> Color.White
            BackgroundStyle.SYSTEM -> Color.White
        }
    }

    /**
     * Check if configuration should show city name
     */
    fun shouldShowCityName(configuration: WidgetConfiguration): Boolean {
        return configuration.showCityName
    }

    /**
     * Check if configuration should show time
     */
    fun shouldShowTime(configuration: WidgetConfiguration): Boolean {
        return configuration.showTime
    }

    /**
     * Get update interval in milliseconds
     */
    fun getUpdateIntervalMillis(configuration: WidgetConfiguration): Long {
        return configuration.updateIntervalMinutes * 60 * 1000L
    }
}
