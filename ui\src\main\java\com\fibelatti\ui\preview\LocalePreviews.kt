package com.fibelatti.ui.preview

import androidx.compose.ui.tooling.preview.Preview

@Preview(
    name = "Locale Preview - PT",
    group = "Localization",
    showBackground = true,
    locale = "pt",
)
@Preview(
    name = "Locale Preview - ES",
    group = "Localization",
    showBackground = true,
    locale = "es",
)
@Preview(
    name = "Locale Preview - FR",
    group = "Localization",
    showBackground = true,
    locale = "fr",
)
@Preview(
    name = "Locale Preview - RU",
    group = "Localization",
    showBackground = true,
    locale = "ru",
)
@Preview(
    name = "Locale Preview - TR",
    group = "Localization",
    showBackground = true,
    locale = "tr",
)
annotation class LocalePreviews
