package com.example.weatherwidgetapp.data

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.floatPreferencesKey
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.example.weatherwidgetapp.domain.model.BackgroundStyle
import com.example.weatherwidgetapp.domain.model.FontSize
import com.example.weatherwidgetapp.domain.model.IconSize
import com.example.weatherwidgetapp.domain.model.WidgetConfiguration
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

val Context.widgetConfigDataStore: DataStore<Preferences> by preferencesDataStore(name = "widget_config_prefs")

/**
 * DataStore implementation for widget configuration persistence
 */
class WidgetConfigDataStore(private val context: Context) {

    companion object {
        // Keys for widget configuration preferences
        fun backgroundStyleKey(appWidgetId: Int) = stringPreferencesKey("background_style_$appWidgetId")
        fun backgroundTransparencyKey(appWidgetId: Int) = floatPreferencesKey("background_transparency_$appWidgetId")
        fun showCityNameKey(appWidgetId: Int) = booleanPreferencesKey("show_city_name_$appWidgetId")
        fun selectedCityIdKey(appWidgetId: Int) = intPreferencesKey("selected_city_id_$appWidgetId")
        fun iconSizeKey(appWidgetId: Int) = stringPreferencesKey("icon_size_$appWidgetId")
        fun fontSizeKey(appWidgetId: Int) = stringPreferencesKey("font_size_$appWidgetId")
        fun hasRoundedCornersKey(appWidgetId: Int) = booleanPreferencesKey("has_rounded_corners_$appWidgetId")
        fun showTimeKey(appWidgetId: Int) = booleanPreferencesKey("show_time_$appWidgetId")
        fun updateIntervalKey(appWidgetId: Int) = intPreferencesKey("update_interval_$appWidgetId")
    }

    /**
     * Get widget configuration for a specific app widget ID
     */
    fun getWidgetConfiguration(appWidgetId: Int): Flow<WidgetConfiguration?> {
        return context.widgetConfigDataStore.data.map { preferences ->
            val backgroundStyle = preferences[backgroundStyleKey(appWidgetId)]?.let { 
                BackgroundStyle.valueOf(it) 
            } ?: return@map null
            
            WidgetConfiguration(
                appWidgetId = appWidgetId,
                backgroundStyle = backgroundStyle,
                backgroundTransparency = preferences[backgroundTransparencyKey(appWidgetId)] ?: 0.8f,
                showCityName = preferences[showCityNameKey(appWidgetId)] ?: true,
                selectedCityId = preferences[selectedCityIdKey(appWidgetId)],
                iconSize = preferences[iconSizeKey(appWidgetId)]?.let { 
                    IconSize.valueOf(it) 
                } ?: IconSize.MEDIUM,
                fontSize = preferences[fontSizeKey(appWidgetId)]?.let { 
                    FontSize.valueOf(it) 
                } ?: FontSize.MEDIUM,
                hasRoundedCorners = preferences[hasRoundedCornersKey(appWidgetId)] ?: true,
                showTime = preferences[showTimeKey(appWidgetId)] ?: true,
                updateIntervalMinutes = preferences[updateIntervalKey(appWidgetId)] ?: 30
            )
        }
    }

    /**
     * Save widget configuration
     */
    suspend fun saveWidgetConfiguration(configuration: WidgetConfiguration) {
        context.widgetConfigDataStore.edit { preferences ->
            val appWidgetId = configuration.appWidgetId
            
            preferences[backgroundStyleKey(appWidgetId)] = configuration.backgroundStyle.name
            preferences[backgroundTransparencyKey(appWidgetId)] = configuration.backgroundTransparency
            preferences[showCityNameKey(appWidgetId)] = configuration.showCityName
            configuration.selectedCityId?.let { 
                preferences[selectedCityIdKey(appWidgetId)] = it 
            }
            preferences[iconSizeKey(appWidgetId)] = configuration.iconSize.name
            preferences[fontSizeKey(appWidgetId)] = configuration.fontSize.name
            preferences[hasRoundedCornersKey(appWidgetId)] = configuration.hasRoundedCorners
            preferences[showTimeKey(appWidgetId)] = configuration.showTime
            preferences[updateIntervalKey(appWidgetId)] = configuration.updateIntervalMinutes
        }
    }

    /**
     * Delete widget configuration for a specific app widget ID
     */
    suspend fun deleteWidgetConfiguration(appWidgetId: Int) {
        context.widgetConfigDataStore.edit { preferences ->
            preferences.remove(backgroundStyleKey(appWidgetId))
            preferences.remove(backgroundTransparencyKey(appWidgetId))
            preferences.remove(showCityNameKey(appWidgetId))
            preferences.remove(selectedCityIdKey(appWidgetId))
            preferences.remove(iconSizeKey(appWidgetId))
            preferences.remove(fontSizeKey(appWidgetId))
            preferences.remove(hasRoundedCornersKey(appWidgetId))
            preferences.remove(showTimeKey(appWidgetId))
            preferences.remove(updateIntervalKey(appWidgetId))
        }
    }

    /**
     * Get all widget configurations
     */
    fun getAllWidgetConfigurations(): Flow<List<WidgetConfiguration>> {
        return context.widgetConfigDataStore.data.map { preferences ->
            val configurations = mutableListOf<WidgetConfiguration>()
            
            // Extract all app widget IDs from preference keys
            val appWidgetIds = preferences.asMap().keys
                .mapNotNull { key ->
                    val keyName = key.name
                    if (keyName.startsWith("background_style_")) {
                        keyName.substringAfter("background_style_").toIntOrNull()
                    } else null
                }
                .distinct()
            
            // Build configuration for each app widget ID
            appWidgetIds.forEach { appWidgetId ->
                val backgroundStyle = preferences[backgroundStyleKey(appWidgetId)]?.let { 
                    BackgroundStyle.valueOf(it) 
                } ?: BackgroundStyle.SYSTEM
                
                configurations.add(
                    WidgetConfiguration(
                        appWidgetId = appWidgetId,
                        backgroundStyle = backgroundStyle,
                        backgroundTransparency = preferences[backgroundTransparencyKey(appWidgetId)] ?: 0.8f,
                        showCityName = preferences[showCityNameKey(appWidgetId)] ?: true,
                        selectedCityId = preferences[selectedCityIdKey(appWidgetId)],
                        iconSize = preferences[iconSizeKey(appWidgetId)]?.let { 
                            IconSize.valueOf(it) 
                        } ?: IconSize.MEDIUM,
                        fontSize = preferences[fontSizeKey(appWidgetId)]?.let { 
                            FontSize.valueOf(it) 
                        } ?: FontSize.MEDIUM,
                        hasRoundedCorners = preferences[hasRoundedCornersKey(appWidgetId)] ?: true,
                        showTime = preferences[showTimeKey(appWidgetId)] ?: true,
                        updateIntervalMinutes = preferences[updateIntervalKey(appWidgetId)] ?: 30
                    )
                )
            }
            
            configurations
        }
    }
}
