Material Photo Widget
=====

[![Language](https://img.shields.io/badge/language-kotlin-brightgreen.svg)](https://www.github.com/fibelatti/photo-widget)
[![License](https://img.shields.io/badge/License-Apache%202.0-blue.svg)](https://opensource.org/licenses/Apache-2.0)
[![Android CI](https://github.com/fibelatti/photo-widget/actions/workflows/android-ci.yml/badge.svg?branch=main)](https://github.com/fibelatti/photo-widget/actions?query=workflow%3A%22Android+CI%22)

Material Photo Widget is as simple as it can be: an Android home screen widget to display a photo or
collection of photos. It is a free, no-ads, open source alternative for people who want to customize
their home screen with their favorite photos.

Downloads
--------

<a href='https://play.google.com/store/apps/details?id=com.fibelatti.photowidget'><img alt='Get it on Google Play' src='https://play.google.com/intl/en_us/badges/images/generic/en_badge_web_generic.png' width='150' /></a>
<a href='https://apt.izzysoft.de/fdroid/index/apk/com.fibelatti.photowidget'><img alt='Get it on IzzyOnDroid' src='https://gitlab.com/IzzyOnDroid/repo/-/raw/master/assets/IzzyOnDroid.png' width='150' /></a>
<a href="https://github.com/fibelatti/photo-widget/releases/latest"><img alt="Get it on GitHub" src="https://github.com/machiav3lli/oandbackupx/blob/034b226cea5c1b30eb4f6a6f313e4dadcbb0ece4/badge_github.png" width="150"></a>

>[!Note]
>
> Downloads from Google Play are verified by Google Play Protect.
>
> Downloads from IzzyOnDroid are verified by [Reproducible Builds](https://android.izzysoft.de/articles/named/iod-rbs-mirrors-clients?lang=en).
>
> Downloads from GitHub can be verified with third-party tools such as [AppVerifier](https://github.com/soupslurpr/AppVerifier) using the following SHA-256 digest:
>
> 3A:36:30:F5:EB:47:82:43:49:46:9F:11:53:D7:2E:02:3A:15:29:39:50:60:44:A2:DB:EF:63:76:08:76:E4:5B

Features
--------

* 5 aspect ratios: square, tall, wide, original and fill widget
* Customize your square widgets with 10 different shapes
* Customize the rounded corners of your tall, wide or original widgets
* Customize the opacity, offset and padding
* Choose each photo or sync the widget with device folders
* Set an optional interval and have photos flip automatically
* Choose the tap action of each widget: none, view next photo, view in full
  screen or open another app

About the project
--------

Photo Widget is a playground to study and explore modern Android development.

Explored topics:
- Clean & beautiful UI built with Jetpack Compose and Google's [material design three](https://m3.material.io/) guidelines, with support for Material You theming
- Android Jetpack, including Jetpack Compose
- Home screen widgets

Contributing
--------

Anyone is welcome to submit bug reports, feature requests and improvement ideas. Submit yours using the [provided templates](https://github.com/fibelatti/photo-widget/issues/new/choose).

Translations
--------

App translations are sourced through [Crowdin](https://crowdin.com/project/material-photo-widget).

If you spot a mistake or would like to see the app localized to your language feel free to submit your contribution.

License
--------

    Copyright 2023 Filipe Belatti

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

Attributions
--------
Vectors and icons by <a href="https://dazzleui.gumroad.com/l/dazzleiconsfree?ref=svgrepo.com" target="_blank">Dazzle Ui</a> in CC Attribution License via <a href="https://www.svgrepo.com/" target="_blank">SVG Repo</a>

Screenshots were generated with <a href="https://screenshots.pro" target="_blank">App Store Screenshots Generator</a>.
