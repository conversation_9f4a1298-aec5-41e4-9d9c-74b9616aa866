package com.example.weatherwidgetapp.presentation.widget.config

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.weatherwidgetapp.data.model.City
import com.example.weatherwidgetapp.data.model.WeatherInfo
import com.example.weatherwidgetapp.db.dao.CityDao
import com.example.weatherwidgetapp.domain.model.BackgroundStyle
import com.example.weatherwidgetapp.domain.model.FontSize
import com.example.weatherwidgetapp.domain.model.IconSize
import com.example.weatherwidgetapp.domain.model.WidgetConfiguration
import com.example.weatherwidgetapp.domain.model.WidgetConfigurationUiState
import com.example.weatherwidgetapp.domain.usecase.GetWidgetConfigUseCase
import com.example.weatherwidgetapp.domain.usecase.SaveWidgetConfigUseCase
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch

/**
 * ViewModel for widget configuration screen
 */
class WidgetConfigViewModel(
    private val getWidgetConfigUseCase: GetWidgetConfigUseCase,
    private val saveWidgetConfigUseCase: SaveWidgetConfigUseCase,
    private val cityDao: CityDao
) : ViewModel() {

    private val _uiState = MutableStateFlow(WidgetConfigurationUiState())
    val uiState: StateFlow<WidgetConfigurationUiState> = _uiState.asStateFlow()

    private val _previewConfiguration = MutableStateFlow(WidgetConfiguration(appWidgetId = -1))
    val previewConfiguration: StateFlow<WidgetConfiguration> = _previewConfiguration.asStateFlow()

    /**
     * Initialize configuration for a specific app widget ID
     */
    fun initializeConfiguration(appWidgetId: Int) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)

                // Load existing configuration or create default
                val configuration = getWidgetConfigUseCase(appWidgetId).first()
                
                // Load available cities
                val cities = loadCities()
                
                // Load weather data for selected city if available
                val weatherData = configuration.selectedCityId?.let { cityId ->
                    loadWeatherDataForCity(cityId)
                } ?: emptyList()

                _uiState.value = WidgetConfigurationUiState(
                    configuration = configuration,
                    availableCities = cities,
                    weatherData = weatherData,
                    isLoading = false
                )
                
                // Initialize preview with current configuration
                _previewConfiguration.value = configuration

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "Failed to load configuration: ${e.message}"
                )
            }
        }
    }

    /**
     * Update background style and refresh preview
     */
    fun updateBackgroundStyle(style: BackgroundStyle) {
        val updatedConfig = _previewConfiguration.value.copy(backgroundStyle = style)
        _previewConfiguration.value = updatedConfig
        updateConfigurationInState(updatedConfig)
    }

    /**
     * Update background transparency and refresh preview
     */
    fun updateBackgroundTransparency(transparency: Float) {
        val updatedConfig = _previewConfiguration.value.copy(backgroundTransparency = transparency)
        _previewConfiguration.value = updatedConfig
        updateConfigurationInState(updatedConfig)
    }

    /**
     * Update city name visibility and refresh preview
     */
    fun updateShowCityName(show: Boolean) {
        val updatedConfig = _previewConfiguration.value.copy(showCityName = show)
        _previewConfiguration.value = updatedConfig
        updateConfigurationInState(updatedConfig)
    }

    /**
     * Update selected city and refresh preview
     */
    fun updateSelectedCity(cityId: Int) {
        viewModelScope.launch {
            val updatedConfig = _previewConfiguration.value.copy(selectedCityId = cityId)
            _previewConfiguration.value = updatedConfig
            updateConfigurationInState(updatedConfig)
            
            // Load weather data for the new city
            val weatherData = loadWeatherDataForCity(cityId)
            _uiState.value = _uiState.value.copy(weatherData = weatherData)
        }
    }

    /**
     * Update icon size and refresh preview
     */
    fun updateIconSize(size: IconSize) {
        val updatedConfig = _previewConfiguration.value.copy(iconSize = size)
        _previewConfiguration.value = updatedConfig
        updateConfigurationInState(updatedConfig)
    }

    /**
     * Update font size and refresh preview
     */
    fun updateFontSize(size: FontSize) {
        val updatedConfig = _previewConfiguration.value.copy(fontSize = size)
        _previewConfiguration.value = updatedConfig
        updateConfigurationInState(updatedConfig)
    }

    /**
     * Update rounded corners setting and refresh preview
     */
    fun updateRoundedCorners(hasRoundedCorners: Boolean) {
        val updatedConfig = _previewConfiguration.value.copy(hasRoundedCorners = hasRoundedCorners)
        _previewConfiguration.value = updatedConfig
        updateConfigurationInState(updatedConfig)
    }

    /**
     * Update time display setting and refresh preview
     */
    fun updateShowTime(showTime: Boolean) {
        val updatedConfig = _previewConfiguration.value.copy(showTime = showTime)
        _previewConfiguration.value = updatedConfig
        updateConfigurationInState(updatedConfig)
    }

    /**
     * Update update interval and refresh preview
     */
    fun updateUpdateInterval(intervalMinutes: Int) {
        val updatedConfig = _previewConfiguration.value.copy(updateIntervalMinutes = intervalMinutes)
        _previewConfiguration.value = updatedConfig
        updateConfigurationInState(updatedConfig)
    }

    /**
     * Save the current configuration
     */
    fun saveConfiguration(): Boolean {
        return try {
            viewModelScope.launch {
                val result = saveWidgetConfigUseCase(_previewConfiguration.value)
                if (result.isFailure) {
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "Failed to save configuration: ${result.exceptionOrNull()?.message}"
                    )
                }
            }
            true
        } catch (e: Exception) {
            _uiState.value = _uiState.value.copy(
                errorMessage = "Failed to save configuration: ${e.message}"
            )
            false
        }
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    private fun updateConfigurationInState(configuration: WidgetConfiguration) {
        _uiState.value = _uiState.value.copy(configuration = configuration)
    }

    private suspend fun loadCities(): List<City> {
        return try {
            // For now using placeholder data, replace with actual DAO call
            listOf(
                City(1, "London", "UK", 51.5074, -0.1278),
                City(2, "Paris", "France", 48.8566, 2.3522),
                City(3, "New York", "USA", 40.7128, -74.0060),
                City(4, "Tokyo", "Japan", 35.6762, 139.6503),
                City(5, "Sydney", "Australia", -33.8688, 151.2093)
            )
        } catch (e: Exception) {
            emptyList()
        }
    }

    private suspend fun loadWeatherDataForCity(cityId: Int): List<WeatherInfo> {
        return try {
            // Placeholder weather data - replace with actual data fetching
            listOf(
                WeatherInfo(
                    cityId = cityId,
                    cityName = "Sample City",
                    temperature = 22.0,
                    temperatureUnit = "°C",
                    condition = "Partly Cloudy",
                    conditionIconCode = "02d"
                )
            )
        } catch (e: Exception) {
            emptyList()
        }
    }
}
