package com.example.weatherwidgetapp.di

import androidx.room.Room
import com.example.weatherwidgetapp.data.WeatherDataStore
import com.example.weatherwidgetapp.db.WeatherDatabase
import org.koin.android.ext.koin.androidContext
import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.dsl.module

val appModule = module {
    // Context
    single { androidContext() }

    // DataStore
    single { WeatherDataStore(androidContext()) }

    // Room Database
    single {
        Room.databaseBuilder(
            androidContext(),
            WeatherDatabase::class.java,
            "weather_database"
        )
        .fallbackToDestructiveMigration()
        .build()
    }

    // DAOs
    single { get<WeatherDatabase>().cityDao() }
    single { get<WeatherDatabase>().weatherDataDao() }

    // Repositories (Uncomment and implement)
    /*
    single { WeatherRepository(get(), get(), get()) } // Assuming API service, CityDao, WeatherDataDao
    */

    // ViewModels (Uncomment and implement for your main app)
    /*
    viewModel { MainViewModel(get()) } // Assuming MainViewModel takes WeatherRepository
    */
}