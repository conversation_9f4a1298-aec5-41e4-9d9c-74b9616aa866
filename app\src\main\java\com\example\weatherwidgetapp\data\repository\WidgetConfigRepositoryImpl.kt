package com.example.weatherwidgetapp.data.repository

import com.example.weatherwidgetapp.data.WidgetConfigDataStore
import com.example.weatherwidgetapp.domain.model.WidgetConfiguration
import com.example.weatherwidgetapp.domain.repository.WidgetConfigRepository
import kotlinx.coroutines.flow.Flow

/**
 * Implementation of WidgetConfigRepository using DataStore
 */
class WidgetConfigRepositoryImpl(
    private val dataStore: WidgetConfigDataStore
) : WidgetConfigRepository {

    override fun getWidgetConfiguration(appWidgetId: Int): Flow<WidgetConfiguration?> {
        return dataStore.getWidgetConfiguration(appWidgetId)
    }

    override suspend fun saveWidgetConfiguration(configuration: WidgetConfiguration) {
        dataStore.saveWidgetConfiguration(configuration)
    }

    override suspend fun deleteWidgetConfiguration(appWidgetId: Int) {
        dataStore.deleteWidgetConfiguration(appWidgetId)
    }

    override fun getAllWidgetConfigurations(): Flow<List<WidgetConfiguration>> {
        return dataStore.getAllWidgetConfigurations()
    }
}
