package com.example.weatherwidgetapp.domain.usecase

import com.example.weatherwidgetapp.domain.model.WidgetConfiguration
import com.example.weatherwidgetapp.domain.repository.WidgetConfigRepository

/**
 * Use case for saving widget configuration
 */
class SaveWidgetConfigUseCase(
    private val repository: WidgetConfigRepository
) {
    
    /**
     * Save widget configuration with validation
     */
    suspend operator fun invoke(configuration: WidgetConfiguration): Result<Unit> {
        return try {
            // Validate configuration
            validateConfiguration(configuration)
            
            // Save configuration
            repository.saveWidgetConfiguration(configuration)
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    private fun validateConfiguration(configuration: WidgetConfiguration) {
        require(configuration.appWidgetId > 0) { "Invalid app widget ID" }
        require(configuration.backgroundTransparency in 0.0f..1.0f) { 
            "Background transparency must be between 0.0 and 1.0" 
        }
        require(configuration.updateIntervalMinutes > 0) { 
            "Update interval must be positive" 
        }
    }
}
