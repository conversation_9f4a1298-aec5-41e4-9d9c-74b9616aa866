package com.example.weatherwidgetapp.di

import com.example.weatherwidgetapp.data.WidgetConfigDataStore
import com.example.weatherwidgetapp.data.repository.WidgetConfigRepositoryImpl
import com.example.weatherwidgetapp.domain.repository.WidgetConfigRepository
import com.example.weatherwidgetapp.domain.usecase.GetWidgetConfigUseCase
import com.example.weatherwidgetapp.domain.usecase.SaveWidgetConfigUseCase
import com.example.weatherwidgetapp.presentation.widget.config.WidgetConfigViewModel
import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.dsl.module

/**
 * Koin module for widget configuration dependencies
 */
val widgetConfigModule = module {
    
    // Data Store
    single { WidgetConfigDataStore(get()) }
    
    // Repository
    single<WidgetConfigRepository> { WidgetConfigRepositoryImpl(get()) }
    
    // Use Cases
    single { GetWidgetConfigUseCase(get()) }
    single { SaveWidgetConfigUseCase(get()) }
    
    // ViewModel
    viewModel { WidgetConfigViewModel(get(), get(), get()) }
}
