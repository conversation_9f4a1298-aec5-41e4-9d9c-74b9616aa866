package com.example.weatherwidgetapp.domain.model

import kotlinx.serialization.Serializable

/**
 * Domain model representing widget customization configuration
 */
@Serializable
data class WidgetConfiguration(
    val appWidgetId: Int,
    val backgroundStyle: BackgroundStyle = BackgroundStyle.SYSTEM,
    val backgroundTransparency: Float = 0.8f, // 0.0 = fully transparent, 1.0 = fully opaque
    val showCityName: Boolean = true,
    val selectedCityId: Int? = null,
    val iconSize: IconSize = IconSize.MEDIUM,
    val fontSize: FontSize = FontSize.MEDIUM,
    val hasRoundedCorners: Boolean = true,
    val showTime: Boolean = true,
    val updateIntervalMinutes: Int = 30 // Update interval in minutes
)

@Serializable
enum class BackgroundStyle {
    DARK,
    LIGHT,
    BLUE,
    SYSTEM // Follows system theme
}

@Serializable
enum class IconSize(val scaleFactor: Float) {
    SMALL(0.8f),
    MEDIUM(1.0f),
    LARGE(1.2f),
    EXTRA_LARGE(1.4f)
}

@Serializable
enum class FontSize(val scaleFactor: Float) {
    SMALL(0.8f),
    MEDIUM(1.0f),
    LARGE(1.2f),
    EXTRA_LARGE(1.4f)
}

/**
 * UI state for widget configuration screen
 */
data class WidgetConfigurationUiState(
    val configuration: WidgetConfiguration = WidgetConfiguration(appWidgetId = -1),
    val availableCities: List<com.example.weatherwidgetapp.data.model.City> = emptyList(),
    val weatherData: List<com.example.weatherwidgetapp.data.model.WeatherInfo> = emptyList(),
    val isLoading: Boolean = false,
    val errorMessage: String? = null
)
