<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="com.android.vending.BILLING" />

    <application
        android:name=".NotepadApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:enableOnBackInvokedCallback="true"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_classic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_classic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Application"
        tools:targetApi="tiramisu">
        <profileable
            android:shell="true"
            tools:targetApi="34" /> <!-- provider for share image -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.file_provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_path" />
        </provider> <!-- pin widget receiver -->
        <receiver
            android:name=".widgets.services.PinWidgetReceiver"
            android:enabled="true"
            android:exported="true" /> <!-- widget receiver -->
        <receiver
            android:name=".widgets.services.NoteWidgetReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/note_widget_info" />
        </receiver> <!-- notification receiver -->
        <receiver
            android:name=".services.notification.NotificationReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.DATE_CHANGED" />
                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
                <action android:name="android.intent.action.TIME_SET" />
                <action android:name="android.app.action.SCHEDULE_EXACT_ALARM_PERMISSION_STATE_CHANGED" />
            </intent-filter>
        </receiver>
        <!-- crash activity -->
        <activity android:name=".crash.CrashActivity" />
        <!-- share receiver activity -->
<!--        <activity-->
<!--            android:name=".activity.ShareReceiverActivity"-->
<!--            android:exported="true">-->
<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.SEND" />-->

<!--                <category android:name="android.intent.category.DEFAULT" />-->

<!--                <data android:mimeType="text/plain" />-->
<!--            </intent-filter>-->
<!--        </activity>-->
        <!-- widget config activity -->
        <activity
            android:name=".widgets.config.ConfigWidgetActivity"
            android:imeOptions="actionSend|flagNoEnterAction"
            android:theme="@style/Theme.Application"
            android:windowSoftInputMode="adjustResize" /> <!-- startup activity -->
        <activity
            android:name=".activity.StartupActivity"
            android:enabled="true"
            android:exported="true"
            android:imeOptions="actionSend|flagNoEnterAction"
            android:theme="@style/Theme.Launcher"
            android:windowSoftInputMode="adjustResize">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <!-- moin activity -->
        <activity
            android:name=".activity.MainActivity"
            android:enabled="true"
            android:exported="true"
            android:imeOptions="actionSend|flagNoEnterAction"
            android:theme="@style/Theme.Launcher"
            android:windowSoftInputMode="adjustResize">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity-alias
            android:name=".MaterialLauncher"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/ic_material_launcher"
            android:noHistory="true"
            android:roundIcon="@mipmap/ic_material_launcher_round"
            android:targetActivity=".activity.MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name=".StrawberryLauncher"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/ic_strawberry_launcher"
            android:noHistory="true"
            android:roundIcon="@mipmap/ic_strawberry_launcher_round"
            android:targetActivity=".activity.MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name=".PlumLauncher"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/ic_plum_launcher"
            android:noHistory="true"
            android:roundIcon="@mipmap/ic_plum_launcher_round"
            android:targetActivity=".activity.MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name=".MangoLauncher"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/ic_mango_launcher"
            android:noHistory="true"
            android:roundIcon="@mipmap/ic_mango_launcher_round"
            android:targetActivity=".activity.MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name=".AvocadoLauncher"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/ic_avocado_launcher"
            android:noHistory="true"
            android:roundIcon="@mipmap/ic_avocado_launcher_round"
            android:targetActivity=".activity.MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name=".BlueberryLauncher"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/ic_blueberry_launcher"
            android:noHistory="true"
            android:roundIcon="@mipmap/ic_blueberry_launcher_round"
            android:targetActivity=".activity.MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name=".NightLauncher"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/ic_night_launcher"
            android:noHistory="true"
            android:roundIcon="@mipmap/ic_night_launcher_round"
            android:targetActivity=".activity.MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name=".StarLauncher"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/ic_star_launcher"
            android:noHistory="true"
            android:roundIcon="@mipmap/ic_star_launcher_round"
            android:targetActivity=".activity.MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name=".MoneyLauncher"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/ic_money_launcher"
            android:noHistory="true"
            android:roundIcon="@mipmap/ic_money_launcher_round"
            android:targetActivity=".activity.MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name=".RocketLauncher"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/ic_rocket_launcher"
            android:noHistory="true"
            android:roundIcon="@mipmap/ic_rocket_launcher_round"
            android:targetActivity=".activity.MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name=".LoveLauncher"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/ic_love_launcher"
            android:noHistory="true"
            android:roundIcon="@mipmap/ic_love_launcher_round"
            android:targetActivity=".activity.MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias> <!-- activity's for any icons -->
        <activity-alias
            android:name=".ClassicWhiteLauncher"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/ic_classic_white_launcher"
            android:noHistory="true"
            android:roundIcon="@mipmap/ic_classic_white_launcher_round"
            android:targetActivity=".activity.MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>
    </application>

</manifest>