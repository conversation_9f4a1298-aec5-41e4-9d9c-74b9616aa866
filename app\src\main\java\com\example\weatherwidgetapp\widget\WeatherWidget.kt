package com.example.weatherwidgetapp.widget

import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.produceState
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.glance.GlanceId
import androidx.glance.GlanceModifier
import androidx.glance.ImageProvider
import androidx.glance.action.clickable
import androidx.glance.appwidget.GlanceAppWidget
import androidx.glance.appwidget.action.actionRunCallback
import androidx.glance.appwidget.provideContent
import androidx.glance.background
import androidx.glance.layout.Alignment
import androidx.glance.layout.Box
import androidx.glance.layout.Column
import androidx.glance.layout.Row
import androidx.glance.layout.Spacer
import androidx.glance.layout.fillMaxSize
import androidx.glance.layout.height
import androidx.glance.layout.padding
import androidx.glance.layout.width
import androidx.glance.text.FontWeight
import androidx.glance.text.Text
import androidx.glance.text.TextStyle
import androidx.glance.unit.ColorProvider
import com.example.weatherwidgetapp.R
import com.example.weatherwidgetapp.data.WeatherDataStore
import com.example.weatherwidgetapp.data.model.WeatherInfo
import com.example.weatherwidgetapp.db.dao.CityDao
import com.example.weatherwidgetapp.db.dao.WeatherDataDao
import com.example.weatherwidgetapp.domain.model.WidgetConfiguration
import com.example.weatherwidgetapp.domain.usecase.GetWidgetConfigUseCase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

object WeatherWidget : GlanceAppWidget(), KoinComponent {

    private val dataStore: WeatherDataStore by inject()
    private val getWidgetConfigUseCase: GetWidgetConfigUseCase by inject()
    private val cityDao: CityDao by inject()
    private val weatherDataDao: WeatherDataDao by inject()

    override suspend fun provideGlance(context: Context, id: GlanceId) {
        provideContent {
            // Get widget configuration
            val configuration by produceState<WidgetConfiguration?>(initialValue = null, key1 = id) {
                try {
                    // Extract app widget ID from glance ID
                    val appWidgetId = extractAppWidgetId(context, id)
                    value = getWidgetConfigUseCase(appWidgetId).first()
                } catch (e: Exception) {
                    value = null
                }
            }

            val weatherInfo by produceState<WeatherInfo?>(initialValue = null, key1 = configuration?.selectedCityId) {
                configuration?.selectedCityId?.let { cityId ->
                    value = fetchWeatherData(context, cityId)
                }
            }

            WeatherWidgetContent(weatherInfo, configuration)
        }
    }

    @Composable
    private fun WeatherWidgetContent(weatherInfo: WeatherInfo?, configuration: WidgetConfiguration?) {
        val config = configuration ?: WidgetConfiguration(appWidgetId = -1)

        Box(
            modifier = WidgetConfigurationHelper.applyBackgroundStyle(
                GlanceModifier
                    .fillMaxSize()
                    .padding(16.dp)
                    .clickable(actionRunCallback<RefreshAction>()),
                config
            ),
            contentAlignment = Alignment.Center
        ) {
            if (weatherInfo != null) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    // City name (if enabled)
                    if (WidgetConfigurationHelper.shouldShowCityName(config)) {
                        Text(
                            text = weatherInfo.cityName,
                            style = WidgetConfigurationHelper.getTextStyle(config, 20, FontWeight.Bold)
                        )
                        Spacer(GlanceModifier.height(8.dp))
                    }

                    // Temperature
                    Text(
                        text = "${weatherInfo.temperature}${weatherInfo.temperatureUnit}",
                        style = WidgetConfigurationHelper.getTextStyle(config, 36, FontWeight.Medium)
                    )

                    Spacer(GlanceModifier.height(4.dp))

                    // Weather condition
                    Text(
                        text = weatherInfo.condition,
                        style = WidgetConfigurationHelper.getTextStyle(config, 16, FontWeight.Normal)
                    )

                    // Time (if enabled)
                    if (WidgetConfigurationHelper.shouldShowTime(config)) {
                        Spacer(GlanceModifier.height(4.dp))
                        Text(
                            text = getCurrentTime(),
                            style = WidgetConfigurationHelper.getTextStyle(config, 12, FontWeight.Normal)
                        )
                    }
                }
            } else {
                Text(
                    text = "Tap to configure",
                    style = WidgetConfigurationHelper.getTextStyle(config, 16, FontWeight.Normal)
                )
            }
        }
    }

    private suspend fun fetchWeatherData(context: Context, cityId: Int): WeatherInfo {
        return withContext(Dispatchers.IO) {
            try {
                // Simulate network/db delay and return dummy data
                kotlinx.coroutines.delay(500)
                when (cityId) {
                    1 -> WeatherInfo(
                        cityId = cityId,
                        cityName = "London",
                        temperature = 15.0,
                        temperatureUnit = "°C",
                        condition = "Cloudy",
                        conditionIconCode = "03d"
                    )
                    2 -> WeatherInfo(
                        cityId = cityId,
                        cityName = "Paris",
                        temperature = 18.0,
                        temperatureUnit = "°C",
                        condition = "Sunny",
                        conditionIconCode = "01d"
                    )
                    3 -> WeatherInfo(
                        cityId = cityId,
                        cityName = "New York",
                        temperature = 22.0,
                        temperatureUnit = "°C",
                        condition = "Partly Cloudy",
                        conditionIconCode = "02d"
                    )
                    4 -> WeatherInfo(
                        cityId = cityId,
                        cityName = "Tokyo",
                        temperature = 25.0,
                        temperatureUnit = "°C",
                        condition = "Clear",
                        conditionIconCode = "01d"
                    )
                    5 -> WeatherInfo(
                        cityId = cityId,
                        cityName = "Sydney",
                        temperature = 20.0,
                        temperatureUnit = "°C",
                        condition = "Rainy",
                        conditionIconCode = "10d"
                    )
                    else -> WeatherInfo(
                        cityId = cityId,
                        cityName = "Unknown City",
                        temperature = 0.0,
                        temperatureUnit = "°C",
                        condition = "No data",
                        conditionIconCode = "50d"
                    )
                }
            } catch (e: Exception) {
                WeatherInfo(
                    cityId = cityId,
                    cityName = "Error",
                    temperature = 0.0,
                    temperatureUnit = "°C",
                    condition = "Failed to load",
                    conditionIconCode = "50d"
                )
            }
        }
    }

    /**
     * Extract app widget ID from Glance ID
     */
    private suspend fun extractAppWidgetId(context: Context, glanceId: GlanceId): Int {
        // This is a simplified approach - in a real implementation you might need
        // to use GlanceAppWidgetManager to get the app widget ID
        return try {
            // For now, return a default ID - this should be properly implemented
            // based on your widget management strategy
            1 // Default widget ID
        } catch (e: Exception) {
            1 // Fallback
        }
    }

    /**
     * Get current time formatted for display
     */
    private fun getCurrentTime(): String {
        val formatter = SimpleDateFormat("HH:mm", Locale.getDefault())
        return formatter.format(Date())
    }
}