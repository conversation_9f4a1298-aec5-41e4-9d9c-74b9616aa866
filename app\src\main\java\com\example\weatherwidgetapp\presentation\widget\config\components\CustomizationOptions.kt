package com.example.weatherwidgetapp.presentation.widget.config.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.ExposedDropdownMenuDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Slider
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.unit.dp
import com.example.weatherwidgetapp.data.model.City
import com.example.weatherwidgetapp.domain.model.BackgroundStyle
import com.example.weatherwidgetapp.domain.model.FontSize
import com.example.weatherwidgetapp.domain.model.IconSize
import com.example.weatherwidgetapp.domain.model.WidgetConfiguration

/**
 * Customization options component with all widget configuration controls
 */
@Composable
fun CustomizationOptions(
    configuration: WidgetConfiguration,
    availableCities: List<City>,
    onBackgroundStyleChanged: (BackgroundStyle) -> Unit,
    onTransparencyChanged: (Float) -> Unit,
    onShowCityNameChanged: (Boolean) -> Unit,
    onCitySelected: (Int) -> Unit,
    onIconSizeChanged: (IconSize) -> Unit,
    onFontSizeChanged: (FontSize) -> Unit,
    onRoundedCornersChanged: (Boolean) -> Unit,
    onShowTimeChanged: (Boolean) -> Unit,
    onUpdateIntervalChanged: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Background Style Section
        CustomizationSection(title = "Background Style") {
            BackgroundStyleSelector(
                selectedStyle = configuration.backgroundStyle,
                onStyleSelected = onBackgroundStyleChanged
            )
        }

        // Background Transparency Section
        CustomizationSection(title = "Background Transparency") {
            TransparencySlider(
                transparency = configuration.backgroundTransparency,
                onTransparencyChanged = onTransparencyChanged
            )
        }

        // Location Settings Section
        CustomizationSection(title = "Location Settings") {
            Column {
                SwitchOption(
                    label = "Show City Name",
                    checked = configuration.showCityName,
                    onCheckedChange = onShowCityNameChanged
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                CitySelector(
                    cities = availableCities,
                    selectedCityId = configuration.selectedCityId,
                    onCitySelected = onCitySelected
                )
            }
        }

        // Visual Elements Section
        CustomizationSection(title = "Visual Elements") {
            Column {
                SizeSelector(
                    label = "Icon Size",
                    selectedSize = configuration.iconSize.name,
                    sizes = IconSize.values().map { it.name },
                    onSizeSelected = { sizeName ->
                        onIconSizeChanged(IconSize.valueOf(sizeName))
                    }
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                SizeSelector(
                    label = "Font Size",
                    selectedSize = configuration.fontSize.name,
                    sizes = FontSize.values().map { it.name },
                    onSizeSelected = { sizeName ->
                        onFontSizeChanged(FontSize.valueOf(sizeName))
                    }
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                SwitchOption(
                    label = "Rounded Corners",
                    checked = configuration.hasRoundedCorners,
                    onCheckedChange = onRoundedCornersChanged
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                SwitchOption(
                    label = "Show Time",
                    checked = configuration.showTime,
                    onCheckedChange = onShowTimeChanged
                )
            }
        }

        // Update Interval Section
        CustomizationSection(title = "Update Interval") {
            UpdateIntervalSelector(
                intervalMinutes = configuration.updateIntervalMinutes,
                onIntervalChanged = onUpdateIntervalChanged
            )
        }
    }
}

@Composable
private fun CustomizationSection(
    title: String,
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.height(8.dp))
            content()
        }
    }
}

@Composable
private fun BackgroundStyleSelector(
    selectedStyle: BackgroundStyle,
    onStyleSelected: (BackgroundStyle) -> Unit
) {
    Column(modifier = Modifier.selectableGroup()) {
        BackgroundStyle.values().forEach { style ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (style == selectedStyle),
                        onClick = { onStyleSelected(style) },
                        role = Role.RadioButton
                    )
                    .padding(vertical = 4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (style == selectedStyle),
                    onClick = null
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = style.name.lowercase().replaceFirstChar { it.uppercase() },
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}

@Composable
private fun TransparencySlider(
    transparency: Float,
    onTransparencyChanged: (Float) -> Unit
) {
    Column {
        Text(
            text = "Transparency: ${(transparency * 100).toInt()}%",
            style = MaterialTheme.typography.bodyMedium
        )
        Slider(
            value = transparency,
            onValueChange = onTransparencyChanged,
            valueRange = 0.1f..1.0f,
            steps = 9
        )
    }
}

@Composable
private fun SwitchOption(
    label: String,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium
        )
        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun CitySelector(
    cities: List<City>,
    selectedCityId: Int?,
    onCitySelected: (Int) -> Unit
) {
    var expanded by remember { mutableStateOf(false) }
    val selectedCity = cities.find { it.id == selectedCityId }

    ExposedDropdownMenuBox(
        expanded = expanded,
        onExpandedChange = { expanded = !expanded }
    ) {
        OutlinedTextField(
            modifier = Modifier
                .menuAnchor()
                .fillMaxWidth(),
            readOnly = true,
            value = selectedCity?.let { "${it.name}, ${it.country}" } ?: "Select City",
            onValueChange = {},
            label = { Text("City") },
            trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
            colors = ExposedDropdownMenuDefaults.outlinedTextFieldColors()
        )
        ExposedDropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false }
        ) {
            cities.forEach { city ->
                DropdownMenuItem(
                    text = { Text("${city.name}, ${city.country}") },
                    onClick = {
                        onCitySelected(city.id)
                        expanded = false
                    }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SizeSelector(
    label: String,
    selectedSize: String,
    sizes: List<String>,
    onSizeSelected: (String) -> Unit
) {
    var expanded by remember { mutableStateOf(false) }

    ExposedDropdownMenuBox(
        expanded = expanded,
        onExpandedChange = { expanded = !expanded }
    ) {
        OutlinedTextField(
            modifier = Modifier
                .menuAnchor()
                .fillMaxWidth(),
            readOnly = true,
            value = selectedSize.lowercase().replaceFirstChar { it.uppercase() },
            onValueChange = {},
            label = { Text(label) },
            trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
            colors = ExposedDropdownMenuDefaults.outlinedTextFieldColors()
        )
        ExposedDropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false }
        ) {
            sizes.forEach { size ->
                DropdownMenuItem(
                    text = { Text(size.lowercase().replaceFirstChar { it.uppercase() }) },
                    onClick = {
                        onSizeSelected(size)
                        expanded = false
                    }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun UpdateIntervalSelector(
    intervalMinutes: Int,
    onIntervalChanged: (Int) -> Unit
) {
    var expanded by remember { mutableStateOf(false) }
    val intervals = listOf(15, 30, 60, 120, 240) // 15 min, 30 min, 1h, 2h, 4h

    ExposedDropdownMenuBox(
        expanded = expanded,
        onExpandedChange = { expanded = !expanded }
    ) {
        OutlinedTextField(
            modifier = Modifier
                .menuAnchor()
                .fillMaxWidth(),
            readOnly = true,
            value = when (intervalMinutes) {
                in 1..59 -> "$intervalMinutes minutes"
                else -> "${intervalMinutes / 60} hour${if (intervalMinutes > 60) "s" else ""}"
            },
            onValueChange = {},
            label = { Text("Update Interval") },
            trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
            colors = ExposedDropdownMenuDefaults.outlinedTextFieldColors()
        )
        ExposedDropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false }
        ) {
            intervals.forEach { interval ->
                DropdownMenuItem(
                    text = { 
                        Text(
                            when (interval) {
                                in 1..59 -> "$interval minutes"
                                else -> "${interval / 60} hour${if (interval > 60) "s" else ""}"
                            }
                        )
                    },
                    onClick = {
                        onIntervalChanged(interval)
                        expanded = false
                    }
                )
            }
        }
    }
}
